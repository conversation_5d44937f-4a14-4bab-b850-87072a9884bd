'use server'

import { signOut } from 'next-auth/react'
import { getServerAuthSession } from '@/config/auth'
import { env } from '@/config/environment'
import { getLocale } from 'next-intl/server'

export interface ApiServiceProps extends RequestInit {
  path: string
  searchParams?: Record<string, any>
  locale?: string
  revalidate?: number | false
}

export async function apiService({ path, searchParams, revalidate, ...props }: ApiServiceProps) {
  try {
    const user = await getServerAuthSession()

    const locale = await getLocale()

    // Validate BASE_API environment variable
    if (!env.BASE_API) {
      console.error('BASE_API environment variable is not set')
    }

    const headers = {
      ...props.headers,
      'Accept-Language': locale || 'en',
      Accept: 'application/json',
      ...(user?.user.token && { Authorization: `Bearer ${user?.user.token}` }),
    }

    const BASE_URL = env.BASE_API
    const urlSearchParams = new URLSearchParams(searchParams)
    const url = `${BASE_URL}${path}${!!urlSearchParams.size ? `?${new URLSearchParams(searchParams)}` : ''}`

    const res = await fetch(url, {
      ...props,
      method: props.method || 'GET',
      headers,
      next: {
        tags: [path, urlSearchParams.toString() || ''],
        ...(revalidate !== undefined && { revalidate }),
        ...props.next,
      },
    })

    if (res.ok) {
      const jsonRes = await res.json()

      return jsonRes
    } else {
      let errorResponse
      try {
        errorResponse = await res.json()
      } catch (e) {
        errorResponse = { message: 'Unable to read error response' }
      }

      if (res.status === 401) {
        return signOut({ callbackUrl: `/auth/login`, redirect: true })
      } else if (res.status === 422) {
        return {
          status: 422,
          errors: errorResponse.errors || errorResponse.message || 'Validation failed',
          message: 'Validation failed',
        }
      } else {
        return {
          status: res.status,
          message: errorResponse.message || res.statusText || 'Request failed',
          error: errorResponse,
        }
      }
    }
  } catch (e) {
    return e
  }
}
