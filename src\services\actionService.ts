'use server'

import { ActionServiceReturn } from '@/types'
import { apiService, ApiServiceProps } from './apiService'

export type ActionServiceProps = ApiServiceProps

export async function actionService<T>(props: ActionServiceProps, _: any, body: any): Promise<ActionServiceReturn<T>> {
  try {
    const res = await apiService({ body, ...props })
    return {
      status: true,
      date: res,
      message: res?.message || 'Success',
    }
  } catch (error: any) {
    return {
      status: false,
      error: error.status === 422 ? error.errors : error.message,
      message: error.message,
    }
  }
}
