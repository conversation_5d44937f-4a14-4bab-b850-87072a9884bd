import useApi from '@/hooks/useApi'
import { actionService } from '@/services'
import { IRegister } from '@/types'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'

const useRegister = () => {
  const t = useTranslations()
  const { action, isPending, state } = useApi({
    path: 'auth/register',
    method: 'POST',
    onSuccess: (res) => {
      toast.success(t('auth.register_success'))
    },
    onError: (error) => {
      console.error('Register error:', error)
      toast.error(t('auth.register_failed'))
    },
  })

  const handleSubmit = async (payload: IRegister) => {
    action(payload)
    // try {
    //   const result = await actionService(
    //     {
    //       path: 'auth/register',
    //       method: 'POST',
    //     },
    //     null,
    //     payload
    //   )

    //   console.log(result)

    //   if (result.status) {
    //     toast.success('تم التسجيل بنجاح')
    //   }
    // } catch (error) {
    //   console.error('خطأ في التسجيل:', error)
    //   toast.error('خطأ في التسجيل')
    // }
  }
  return {
    t,
    isPending,
    handleSubmit,
  }
}

export default useRegister
