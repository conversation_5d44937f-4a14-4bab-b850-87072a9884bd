import useApi from '@/hooks/useApi'
import { actionService } from '@/services'
import { IRegister } from '@/types'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'
import { useTransition } from 'react'

const useRegister = () => {
  const t = useTranslations()
  const [isPending, startTransition] = useTransition()
  const { action } = useApi({
    path: 'auth/register',
    method: 'POST',
    onSuccess: (res) => {
      toast.success(t('auth.register_success'))
    },
    onError: (error) => {
      console.error('Register error:', error)
      toast.error(t('auth.register_failed'))
    },
  })

  const handleSubmit = (payload: IRegister) => {
    startTransition(() => {
      action(payload)
    })
  }
  return {
    t,
    isPending,
    handleSubmit,
  }
}

export default useRegister
