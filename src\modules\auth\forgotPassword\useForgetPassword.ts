'use client'

import { useTranslations } from 'next-intl'
import { IForgetPassword } from '@/types'
import { toast } from 'sonner'
import { setCookie } from 'cookies-next'
import { redirect } from 'next/navigation'
import useApi from '@/hooks/useApi'

const defaultValues: IForgetPassword = {
  email: '',
}

const useForgetPassword = () => {
  const t = useTranslations()

  const { action, isPending } = useApi({
    path: 'auth/forgot-password',
    method: 'POST',
    onSuccess: (res) => {
      toast.success(t('auth.reset_code_sent'))
      setCookie('em', res.email)
      redirect('/auth/verify-reset-code')
    },
    onError: (error) => {
      console.error('Forgot password error:', error)
      toast.error(t('auth.failed_to_send_code'))
    },
  })

  const handleSubmit = (payload: IForgetPassword) => {
    action(payload)
  }

  return {
    t,
    isPending,
    handleSubmit,
    defaultValues,
  }
}

export default useForgetPassword
