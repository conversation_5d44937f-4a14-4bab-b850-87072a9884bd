'use client'

import { resetPasswordSchema } from '../schema'
import useResetPassword from './useResetPassword'
import HeaderPage from '../components/HeaderPage'
import { Button } from '@/components/ui/button'
import { FormWrapper } from '@/components/core/FormWrapper'
import { FormPasswordInput } from '@/components/form/FormPasswordInput'

const ResetPassword = () => {
  const { t, onSubmit, defaultValues, isLoading, hasRequiredData } = useResetPassword()

  if (!hasRequiredData) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px]">
        <div className="text-center">
          <p className="text-lg text-gray-600 mb-4">{t('auth.loading')}</p>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        </div>
      </div>
    )
  }

  return (
    <>
      <HeaderPage title="reset_password" description="enter_new_password" />
      <FormWrapper defaultValues={defaultValues} onSubmit={onSubmit} schema={resetPasswordSchema}>
        <div className="flex flex-col gap-4">
          <FormPasswordInput
            className="min-w-[300px] sm:min-w-[380px]"
            name="password"
            label={t('label.new_password')}
            placeholder={t('label.new_password')}
            disabled={isLoading}
          />
          <FormPasswordInput
            name="password_confirmation"
            label={t('label.new_password_confirmation')}
            placeholder={t('label.new_password_confirmation')}
            disabled={isLoading}
          />
          <Button type="submit" className="w-full mt-11" disabled={isLoading}>
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {t('auth.saving')}
              </div>
            ) : (
              t('auth.save')
            )}
          </Button>
        </div>
      </FormWrapper>
    </>
  )
}

export default ResetPassword
