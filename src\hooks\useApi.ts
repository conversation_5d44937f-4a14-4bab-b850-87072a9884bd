'use client'
import { actionService, ActionServiceProps } from '@/services/actionService'
import { ActionServiceReturn } from '@/types'
import { useActionState, useEffect } from 'react'

interface Props extends ActionServiceProps {
  handleError?: boolean
  handleSuccess?: boolean
  onSuccess?: (res: any) => void
  onError?: (error: any) => void
}

interface ApiReturn<T> {
  state: ActionServiceReturn<T>
  action: (payload: any) => void
  isPending: boolean
}

const useApi = <T>({ handleError = true, handleSuccess = true, onSuccess, onError, ...props }: Props): ApiReturn<T> => {
  const serverAction = actionService.bind(null, props)
  const [state, action, isPending] = useActionState(serverAction, { status: false })

  useEffect(() => {
    if (state.status) {
      onSuccess && onSuccess(state.date)

      if (handleSuccess) {
        // if handleSuccess true handle success
      }
    } else if (state.error) {
      onError && onError(state.error)

      if (handleError) {
        // if handleError true use useError
      }
    }
  }, [state])

  return { state: state as ActionServiceReturn<T>, action, isPending }
}

export default useApi
