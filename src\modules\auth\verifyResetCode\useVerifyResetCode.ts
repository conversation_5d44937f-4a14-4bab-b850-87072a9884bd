import { getCookie, set<PERSON><PERSON><PERSON> } from 'cookies-next'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'
import { useEffect, useRef } from 'react'

import { IVerifyResetCode } from '@/types'
import { IFormWrapper } from '@/components/core/FormWrapper'
import { actionService } from '@/services'
import { toast } from 'sonner'

const defaultValues: IVerifyResetCode = {
  email: '',
  code: '',
}

const useVerifyResetCode = () => {
  const t = useTranslations()
  const email = getCookie('em')
  const formRef = useRef<IFormWrapper>(null)
  const onSubmit = async (payload: IVerifyResetCode) => {
    payload.email = email ?? ''
    payload.code = formRef.current?.getValues('code') ?? ''

    try {
      const result = await actionService(
        {
          path: 'auth/verify-reset-code',
          method: 'POST',
        },
        null,
        payload
      )

      if (result.status) {
        toast.success(t('auth.code_verified_successfully'))
        setCookie('code', payload.code)
        redirect('/auth/reset-password')
      } else {
        toast.error(result.message || t('auth.invalid_code'))
      }
    } catch (error) {
      console.error('Verify reset code error:', error)
      toast.error(t('auth.invalid_code'))
    }
  }

  const handleResendCode = async () => {}

  useEffect(() => {
    const handlePopState = () => {
      sessionStorage.removeItem('counter')
    }

    window.addEventListener('popstate', handlePopState)
  }, [])

  return {
    t,
    email,
    onSubmit,
    defaultValues,
    handleResendCode,
    formRef,
  }
}

export default useVerifyResetCode
