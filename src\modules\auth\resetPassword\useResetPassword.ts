import { actionService } from '@/services'
import { IResetPassword } from '@/types'
import { getCookie, deleteCookie } from 'cookies-next'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'
import { toast } from 'sonner'
import { useEffect, useState } from 'react'

const defaultValues: IResetPassword = {
  email: '',
  code: '',
  password: '',
  password_confirmation: '',
}

const useResetPassword = () => {
  const t = useTranslations()
  const [isLoading, setIsLoading] = useState(false)
  const code = getCookie('code')
  const email = getCookie('em')

  useEffect(() => {
    if (!email || !code) {
      toast.error(t('auth.session_expired'))
      redirect('/auth/forgot-password')
    }
  }, [email, code, t])

  const onSubmit = async (payload: IResetPassword) => {
    if (!email || !code) {
      toast.error(t('auth.missing_required_data'))
      return
    }

    setIsLoading(true)

    const submitData = {
      ...payload,
      email: email as string,
      code: code as string,
    }

    try {
      const result = await actionService(
        {
          path: 'auth/reset-password',
          method: 'POST',
        },
        null,
        submitData
      )

      if (result.status) {
        toast.success(t('auth.password_reset_success'))

        deleteCookie('code')
        deleteCookie('em')

        redirect('/auth/login')
      } else {
        toast.error(result.message || t('auth.password_reset_failed'))
      }
    } catch (error) {
      console.error('Reset password error:', error)
      toast.error(t('auth.password_reset_failed'))
    } finally {
      setIsLoading(false)
    }
  }

  return {
    t,
    onSubmit,
    defaultValues,
    isLoading,
    hasRequiredData: !!(email && code),
  }
}

export default useResetPassword
